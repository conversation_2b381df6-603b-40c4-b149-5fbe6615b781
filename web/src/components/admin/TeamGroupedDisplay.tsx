"use client";

import React, { useState, use<PERSON>emo, ReactNode } from "react";
import { FiChevronDown, FiChevronRight, FiUsers } from "react-icons/fi";
import { Button } from "@/components/ui/button";
import { useUserTeams } from "@/lib/hooks";

// Generic types for team grouping
export interface TeamGroupableItem {
  id: number | string;
  is_public?: boolean;
  user_teams?: number[];
  groups?: number[];
}

export interface TeamGroup<T extends TeamGroupableItem> {
  teamId: string;
  teamName: string;
  items: T[];
  count: number;
}

export interface TeamGroupedDisplayProps<T extends TeamGroupableItem> {
  items: T[];
  renderContent: (items: T[], teamId?: string) => ReactNode;
  title?: string;
  className?: string;
  defaultExpanded?: boolean;
}

// Hook for grouping items by team
export function useTeamGrouping<T extends TeamGroupableItem>(items: T[]) {
  const { data: userTeams } = useUserTeams();

  return useMemo(() => {
    const teamGroups: Record<string, T[]> = {};

    // Group items by team
    items.forEach((item) => {
      if (item.is_public || !item.user_teams || item.user_teams.length === 0) {
        // Public items go to "public" group
        if (!teamGroups["public"]) {
          teamGroups["public"] = [];
        }
        teamGroups["public"].push(item);
      } else {
        // Private items go to their assigned teams
        item.user_teams.forEach((teamId) => {
          const teamKey = teamId.toString();
          if (!teamGroups[teamKey]) {
            teamGroups[teamKey] = [];
          }
          teamGroups[teamKey].push(item);
        });
      }
    });

    // Convert to sorted team groups
    const sortedTeamIds = Object.keys(teamGroups).sort((a, b) => {
      if (a === "public") return -1;
      if (b === "public") return 1;

      const teamNameA = userTeams?.find(t => t.id.toString() === a)?.name || `Team ${a}`;
      const teamNameB = userTeams?.find(t => t.id.toString() === b)?.name || `Team ${b}`;
      return teamNameA.localeCompare(teamNameB);
    });

    const teamGroupsArray: TeamGroup<T>[] = sortedTeamIds.map((teamId) => ({
      teamId,
      teamName: teamId === "public" 
        ? "Public" 
        : userTeams?.find(t => t.id.toString() === teamId)?.name || `Team ${teamId}`,
      items: teamGroups[teamId],
      count: teamGroups[teamId].length,
    }));

    return {
      teamGroups: teamGroupsArray,
      totalItems: items.length,
    };
  }, [items, userTeams]);
}

// Team section header component
interface TeamSectionHeaderProps {
  teamName: string;
  count: number;
  isOpen: boolean;
  onToggle: () => void;
}

function TeamSectionHeader({ teamName, count, isOpen, onToggle }: TeamSectionHeaderProps) {
  return (
    <div
      onClick={onToggle}
      className="border-border dark:hover:bg-neutral-800 dark:border-neutral-700 group hover:bg-background-settings-hover/20 bg-background-sidebar py-4 px-4 rounded-sm border cursor-pointer flex items-center justify-between"
    >
      <div className="flex items-center gap-x-3">
        <div className="cursor-pointer">
          {isOpen ? (
            <FiChevronDown size={20} />
          ) : (
            <FiChevronRight size={20} />
          )}
        </div>
        <div className="flex items-center gap-x-2">
          {teamName === "Public" ? (
            <FiUsers size={20} className="text-neutral-500" />
          ) : (
            <FiUsers size={20} className="text-blue-500" />
          )}
          <span className="text-xl font-semibold">{teamName}</span>
        </div>
      </div>
      <div className="text-sm text-neutral-500 dark:text-neutral-300">
        {count} item{count !== 1 ? 's' : ''}
      </div>
    </div>
  );
}

// Main team grouped display component
export function TeamGroupedDisplay<T extends TeamGroupableItem>({
  items,
  renderContent,
  title,
  className = "",
  defaultExpanded = false,
}: TeamGroupedDisplayProps<T>) {
  const { teamGroups } = useTeamGrouping(items);
  const [expandedTeams, setExpandedTeams] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    teamGroups.forEach(group => {
      initial[group.teamId] = defaultExpanded;
    });
    return initial;
  });

  const toggleTeam = (teamId: string) => {
    setExpandedTeams(prev => ({
      ...prev,
      [teamId]: !prev[teamId]
    }));
  };

  const toggleAllTeams = () => {
    const shouldExpandAll = Object.values(expandedTeams).some(expanded => !expanded);
    const newState: Record<string, boolean> = {};
    teamGroups.forEach(group => {
      newState[group.teamId] = shouldExpandAll;
    });
    setExpandedTeams(newState);
  };

  if (teamGroups.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {title && (
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">{title}</h2>
          <Button className="h-9" onClick={toggleAllTeams}>
            {Object.values(expandedTeams).some(expanded => !expanded) ? "Expand All" : "Collapse All"}
          </Button>
        </div>
      )}

      <div className="space-y-4">
        {teamGroups.map((group) => {
          const isExpanded = expandedTeams[group.teamId];
          
          return (
            <div key={group.teamId}>
              <TeamSectionHeader
                teamName={group.teamName}
                count={group.count}
                isOpen={isExpanded}
                onToggle={() => toggleTeam(group.teamId)}
              />
              
              {isExpanded && (
                <div className="mx-1 sm:mx-2 my-2 border border-dashed border-neutral-300 dark:border-neutral-600 rounded-lg bg-neutral-50/50 dark:bg-neutral-900/20 p-2 sm:p-3">
                  {renderContent(group.items, group.teamId)}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
