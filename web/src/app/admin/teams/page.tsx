"use client";

import { ThreeDotsLoader } from "@/components/Loading";
import { AdminPageTitle } from "@/components/admin/Title";
import { FiUsers, FiEdit2 } from "react-icons/fi";
import { ErrorCallout } from "@/components/ErrorCallout";
import useSWR, { mutate } from "swr";
import { deleteTeam } from "./lib";
import { Team } from "./types";
import { usePopup } from "@/components/admin/connectors/Popup";
import { DeleteButton } from "@/components/DeleteButton";
import { Separator } from "@/components/ui/separator";
import Text from "@/components/ui/text";
import CreateButton from "@/components/ui/createButton";
import { TeamForm } from "./TeamForm";
import { useState } from "react";

export default function Page() {
  return (
    <div className="mx-auto container">
      <AdminPageTitle title="Teams" icon={<FiUsers size={32} />} />
      <Main />
    </div>
  );
}

function Main() {
  const { popup, setPopup } = usePopup();
  const [showCreateUpdateForm, setShowCreateUpdateForm] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<Team | undefined>();

  const { data: teams, isLoading, error } = useSWR<Team[]>(
    "/api/manage/admin/user-teams",
    { fetcher: (url) => fetch(url).then((r) => r.json()) }
  );

  const handleEdit = (team: Team) => {
    setSelectedTeam(team);
    setShowCreateUpdateForm(true);
  };

  if (isLoading) return <ThreeDotsLoader />;

  if (error) {
    return (
      <ErrorCallout
        errorTitle="Failed to fetch Teams"
        errorMsg={error?.info?.detail || error.toString()}
      />
    );
  }

  const newTeamButton = (
    <CreateButton
      onClick={() => setShowCreateUpdateForm(true)}
      text="Create Team"
    />
  );

  // ✅ Filter out outdated teams
  const upToDateTeams = teams?.filter((team) => team.is_up_to_date);

  if (!upToDateTeams || upToDateTeams.length === 0) {
    return (
      <div>
        {popup}
        <Text>No teams found.</Text>
        {newTeamButton}

        {showCreateUpdateForm && (
          <TeamForm
            onCreateTeam={() => mutate("/api/manage/admin/user-teams")}
            onClose={() => {
              setShowCreateUpdateForm(false);
              setSelectedTeam(undefined);
            }}
            setPopup={setPopup}
            team={selectedTeam}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {popup}
      {newTeamButton}

      <Separator />

      <table className="w-full text-left border-collapse">
        <thead>
          <tr className="border-b">
            <th className="py-2 px-4">Teams</th>
            <th className="py-2 px-4">Users</th>
            <th className="py-2 px-4">Delete</th>
          </tr>
        </thead>
        <tbody>
          {upToDateTeams.map((team) => (
            <tr key={team.id} className="border-b">
              <td className="py-2 px-4">
                <div
                  className="
                    my-auto flex mb-1 w-fit
                    hover:bg-accent-background-hovered cursor-pointer
                    p-2 rounded-lg border-border text-sm
                  "
                  onClick={() => handleEdit(team)}
                >
                  <FiEdit2 className="my-auto mr-2" />
                  {team.name}
                </div>
              </td>
              <td className="py-2 px-4">
                <UsersCell users={team.users} />
              </td>
              <td className="py-2 px-4">
                <DeleteButton
                  onClick={async () => {
                    const response = await deleteTeam(team.id);
                    if (!response.ok) {
                      const errorMsg = await response.text();
                      setPopup({
                        type: "error",
                        message: `Failed to delete team: ${errorMsg}`,
                      });
                      return;
                    }
                    mutate("/api/manage/admin/user-teams");
                  }}
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {showCreateUpdateForm && (
        <TeamForm
          onCreateTeam={() => mutate("/api/manage/admin/user-teams")}
          onClose={() => {
            setShowCreateUpdateForm(false);
            setSelectedTeam(undefined);
          }}
          setPopup={setPopup}
          team={selectedTeam}
        />
      )}
    </div>
  );
}

// ✅ Small helper: inline toggle for `+N more`
function UsersCell({ users }: { users: { id: string; email: string }[] }) {
  const [showAll, setShowAll] = useState(false);

  const visibleUsers = showAll ? users : users.slice(0, 2);
  const hiddenCount = users.length - 2;

  return (
    <div>
      {visibleUsers.map((u) => (
        <div key={u.id} className="flex my-0.5">
          {u.email}
        </div>
      ))}
      {!showAll && hiddenCount > 0 && (
        <button
          onClick={() => setShowAll(true)}
          className="text-blue-600 hover:underline cursor-pointer text-sm"
        >
          + {hiddenCount} more
        </button>
      )}
    </div>
  );
}

