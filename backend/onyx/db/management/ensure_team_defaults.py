#!/usr/bin/env python3
"""
Management script to ensure all active teams have default LLM providers.
This can be run manually or as part of deployment scripts.
"""

import sys
from sqlalchemy.orm import Session

from onyx.db.engine import get_session_context_manager
from onyx.db.llm import ensure_all_teams_have_default_llm_provider
from onyx.utils.logger import setup_logger

logger = setup_logger()


def main():
    """Ensure all active teams have default LLM providers."""
    logger.info("Starting team default LLM provider assignment...")
    
    try:
        with get_session_context_manager() as db_session:
            ensure_all_teams_have_default_llm_provider(db_session)
            logger.info("✅ Successfully ensured all teams have default LLM providers")
            
    except Exception as e:
        logger.error(f"❌ Failed to ensure team defaults: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
